import numpy as np


class GRU_HLR:
    
    def __init__(self):
        self.epsilon = 0.01
        self.state_limit = int(2.0 / self.epsilon)
        
    def init_state(self, difficulty):
        """
        初始化状态
        """
        initial_strength = max(0.1, 1.0 - (difficulty - 1) * 0.1)
        initial_stability = max(0.1, 1.0 - (difficulty - 1) * 0.05)
        return [initial_strength, initial_stability]
    
    def state2halflife(self, state):
        """
        将状态转换为半衰期
        """
        if state is None or len(state) < 2:
            return 7.0
        
        strength, stability = state[0], state[1]
        halflife = max(1.0, stability * 10.0 * (1.0 + strength))
        return min(halflife, 365.0)  # 限制最大半衰期
    
    def next_state(self, current_state, recall_result):
        """
        根据当前状态和回忆结果计算下一个状态
        """
        if current_state is None or len(current_state) < 2:
            return [0.5, 0.5]
        
        strength, stability = current_state[0], current_state[1]
        
        if recall_result == 1:  # 成功回忆
            new_strength = min(1.0, strength + 0.1)
            new_stability = min(1.0, stability + 0.05)
        else:  # 失败回忆
            new_strength = max(0.1, strength - 0.2)
            new_stability = max(0.1, stability - 0.1)
        
        return [new_strength, new_stability]
