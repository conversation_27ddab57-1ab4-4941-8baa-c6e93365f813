import torch.nn as nn
import torch
import torch.nn.functional as F
from torch.distributions import Categorical
import numpy as np
import warnings
warnings.filterwarnings('ignore')

# PPO策略
class PPOPolicy(nn.Module):
    def __init__(self, gru_model, feature_dim=64, hidden_dim=128, dropout_rate=0.1):
        super().__init__()
        self.gru = gru_model
        self.hidden_state = None
        self.feature_dim = feature_dim
        self.hidden_dim = hidden_dim
        self.dropout_rate = dropout_rate

        self.feature_extractor = nn.Sequential(
            nn.Linear(self.feature_dim, self.feature_dim),
            nn.ReLU(),
            nn.Dropout(dropout_rate)
        )

        input_dim = self.feature_dim + 1

        self.actor = nn.Sequential(
            nn.Linear(input_dim, hidden_dim),
            nn.LayerNorm(hidden_dim),
            nn.ReLU(),
            nn.Dropout(dropout_rate),
            nn.Linear(hidden_dim, hidden_dim // 2),
            nn.ReLU(),
            nn.Linear(hidden_dim // 2, 2)
        )

        self.critic = nn.Sequential(
            nn.Linear(input_dim, hidden_dim),
            nn.LayerNorm(hidden_dim),
            nn.ReLU(),
            nn.Dropout(dropout_rate),
            nn.Linear(hidden_dim, hidden_dim // 2),
            nn.ReLU(),
            nn.Linear(hidden_dim // 2, 1)
        )

        self._initialize_weights()

    def _initialize_weights(self):
        for module in self.modules():
            if isinstance(module, nn.Linear):
                nn.init.orthogonal_(module.weight, gain=np.sqrt(2))
                nn.init.constant_(module.bias, 0.0)
            elif isinstance(module, nn.LayerNorm):
                nn.init.constant_(module.weight, 1.0)
                nn.init.constant_(module.bias, 0.0)

    def forward(self, obs):
        try:
            gru_features = self._extract_gru_features(obs)
            days_since_last = self._process_temporal_feature(obs)

            policy_input = torch.cat([gru_features, days_since_last], dim=1)

            if policy_input.shape[1] != self.feature_dim + 1:
                policy_input = self._handle_dimension_mismatch(policy_input)

            actor_output = self.actor(policy_input)
            critic_output = self.critic(policy_input)

            action_probs = F.softmax(actor_output, dim=-1)
            action_probs = torch.clamp(action_probs, min=1e-8, max=1-1e-8)

            return Categorical(probs=action_probs), critic_output

        except Exception as e:
            return self._fallback_forward()

    def _extract_gru_features(self, obs):
        r_hist = torch.FloatTensor(obs["r_history"]).unsqueeze(0)
        t_hist = torch.FloatTensor(obs["t_history"]).unsqueeze(0)
        p_hist = torch.FloatTensor(obs["p_history"]).unsqueeze(0)

        combined = torch.cat([r_hist, t_hist, p_hist], dim=-1)

        # 移除torch.no_grad()以保持梯度信息
        try:
            output, _ = self.gru(combined, None)
        except:
            try:
                output = self.gru(combined)
                if isinstance(output, tuple):
                    output = output[0]
            except:
                # 如果GRU失败，创建可训练的零张量
                output = torch.zeros(1, combined.shape[-1], self.feature_dim, requires_grad=True)

        gru_feat = self._process_gru_output(output)
        gru_feat = self.feature_extractor(gru_feat)
        return gru_feat

    def _process_gru_output(self, output):
        if isinstance(output, torch.Tensor):
            if len(output.shape) == 3:
                gru_feat = output[:, -1, :]
            elif len(output.shape) == 2:
                gru_feat = output
            elif len(output.shape) == 1:
                gru_feat = output.unsqueeze(0)
            else:
                gru_feat = output.flatten().unsqueeze(0)
        else:
            # 创建可训练的零张量
            gru_feat = torch.zeros(1, self.feature_dim, requires_grad=True)

        if gru_feat.shape[1] != self.feature_dim:
            if gru_feat.shape[1] > self.feature_dim:
                gru_feat = gru_feat[:, :self.feature_dim]
            else:
                # 使用可训练的padding
                padding = torch.zeros(gru_feat.shape[0], self.feature_dim - gru_feat.shape[1],
                                    requires_grad=True, dtype=gru_feat.dtype, device=gru_feat.device)
                gru_feat = torch.cat([gru_feat, padding], dim=1)

        return gru_feat

    def _process_temporal_feature(self, obs):
        days_since_last = torch.FloatTensor([obs["days_since_last"]]).reshape(1, 1)
        days_since_last = torch.clamp(days_since_last, 0, 365) / 365.0
        return days_since_last

    def _handle_dimension_mismatch(self, policy_input):
        target_dim = self.feature_dim + 1
        current_dim = policy_input.shape[1]

        if current_dim > target_dim:
            return policy_input[:, :target_dim]
        elif current_dim < target_dim:
            # 使用可训练的padding
            padding = torch.zeros(policy_input.shape[0], target_dim - current_dim,
                                requires_grad=True, dtype=policy_input.dtype, device=policy_input.device)
            return torch.cat([policy_input, padding], dim=1)
        return policy_input

    def _fallback_forward(self):
        dummy_probs = torch.tensor([[0.5, 0.5]])
        dummy_value = torch.zeros(1, 1)
        return Categorical(probs=dummy_probs), dummy_value

    def get_action(self, obs, deterministic=False):
        with torch.no_grad():
            dist, value = self.forward(obs)
            if deterministic:
                action = torch.argmax(dist.probs, dim=-1)
            else:
                action = dist.sample()
            log_prob = dist.log_prob(action)
            return action, log_prob, value

    def evaluate_actions(self, obs, actions):
        dist, value = self.forward(obs)
        log_probs = dist.log_prob(actions)
        entropy = dist.entropy()
        return log_probs, value, entropy