from collections import deque
from random import random, sample
import math
import torch
import gym
import numpy as np
from gym.vector.utils import spaces
from torch.nn import RNN
import warnings
warnings.filterwarnings('ignore')

# 状态空间
class MemoryEnv(gym.Env):
    def __init__(self, gru_model, max_history=10, difficulty_level=1.0, reward_scale=1.0):
        super().__init__()

        self.gru = gru_model.eval()
        for p in self.gru.parameters():
            p.requires_grad_(False)

        self.action_space = spaces.Discrete(2)

        self.observation_space = spaces.Dict({
            "r_history": spaces.Box(-1, 1, (max_history, 1), dtype=np.float32),
            "t_history": spaces.Box(-1, 365, (max_history, 1), dtype=np.float32),
            "p_history": spaces.Box(-1, 1, (max_history, 1), dtype=np.float32),
            "days_since_last": spaces.Box(0, 365, (1,), dtype=np.float32)
        })

        self.max_history = max_history
        self.difficulty_level = difficulty_level
        self.reward_scale = reward_scale

        self.r_queue = deque(maxlen=max_history)
        self.t_queue = deque(maxlen=max_history)
        self.p_queue = deque(maxlen=max_history)

        self.days_since_last = 0
        self.current_half_life = 7.0
        self.episode_length = 0
        self.max_episode_length = 100

        self.total_reward = 0
        self.review_count = 0
        self.skip_count = 0

        self.forgetting_curve_params = {
            'initial_strength': 1.0,
            'decay_rate': 0.5,
            'min_half_life': 1.0,
            'max_half_life': 30.0
        }

    def reset(self, seed=None, options=None):
        super().reset(seed=seed)
        if seed is not None:
            np.random.seed(seed)

        self.r_queue = deque([-1.0] * self.max_history, maxlen=self.max_history)
        self.t_queue = deque([-1.0] * self.max_history, maxlen=self.max_history)
        self.p_queue = deque([-1.0] * self.max_history, maxlen=self.max_history)

        self.days_since_last = 0
        self.episode_length = 0
        self.current_half_life = 7.0 + np.random.normal(0, 1.0)
        self.current_half_life = np.clip(self.current_half_life, 3.0, 15.0)

        self.total_reward = 0
        self.review_count = 0
        self.skip_count = 0

        obs = self._get_obs()
        info = self._get_info()
        return obs, info

    def step(self, action):
        assert self.action_space.contains(action), f"Invalid action {action}"

        self.episode_length += 1

        if action == 1:
            self._update_history()
            self.days_since_last = 0
            self.review_count += 1
        else:
            self._add_placeholder()
            self.days_since_last += 1
            self.skip_count += 1

        self.current_half_life = self._predict_half_life()
        reward = self._calculate_reward(action)
        self.total_reward += reward

        terminated = self.days_since_last > 30
        truncated = self.episode_length >= self.max_episode_length
        done = terminated or truncated

        obs = self._get_obs()
        info = self._get_info()
        info.update({
            'terminated': terminated,
            'truncated': truncated,
            'episode_length': self.episode_length,
            'total_reward': self.total_reward
        })

        return obs, reward, done, info

    def _get_obs(self):
        return {
            "r_history": np.array(self.r_queue).reshape(-1, 1).astype(np.float32),
            "t_history": np.array(self.t_queue).reshape(-1, 1).astype(np.float32),
            "p_history": np.array(self.p_queue).reshape(-1, 1).astype(np.float32),
            "days_since_last": np.array([self.days_since_last], dtype=np.float32)
        }

    def _get_info(self):
        current_p = np.exp(np.log(0.5) * self.days_since_last / self.current_half_life)
        return {
            'current_half_life': self.current_half_life,
            'current_probability': current_p,
            'days_since_last': self.days_since_last,
            'review_count': self.review_count,
            'skip_count': self.skip_count,
            'review_ratio': self.review_count / max(1, self.review_count + self.skip_count),
            'difficulty_level': self.difficulty_level
        }

    def _update_history(self):
        current_p = np.exp(np.log(0.5) * self.days_since_last / self.current_half_life)

        success_prob = current_p * (1 + 0.1 * np.random.normal())
        success_prob = np.clip(success_prob, 0.1, 0.95)

        recall_result = 1 if np.random.random() < success_prob else 0

        self.r_queue.append(recall_result)
        self.t_queue.append(float(self.days_since_last))
        self.p_queue.append(current_p)

    def _add_placeholder(self):
        self.r_queue.append(-1.0)
        self.t_queue.append(-1.0)
        self.p_queue.append(-1.0)

    def _predict_half_life(self):
        try:
            with torch.no_grad():
                r_tensor = torch.FloatTensor(list(self.r_queue)).unsqueeze(0).unsqueeze(-1)
                t_tensor = torch.FloatTensor(list(self.t_queue)).unsqueeze(0).unsqueeze(-1)
                p_tensor = torch.FloatTensor(list(self.p_queue)).unsqueeze(0).unsqueeze(-1)
                combined = torch.cat([r_tensor, t_tensor, p_tensor], dim=-1)

                try:
                    output = self.gru(combined)
                    if isinstance(output, tuple):
                        output = output[0]
                except Exception:
                    try:
                        output, _ = self.gru(combined, None)
                    except Exception:
                        try:
                            hidden_size = getattr(self.gru, 'hidden_size', 64)
                            hidden_state = torch.zeros(1, 1, hidden_size)
                            output, _ = self.gru(combined, hidden_state)
                        except Exception:
                            return self._fallback_half_life_prediction()

                predicted_half_life = self._extract_scalar_from_tensor(output)

                predicted_half_life = np.clip(
                    predicted_half_life,
                    self.forgetting_curve_params['min_half_life'],
                    self.forgetting_curve_params['max_half_life']
                )

                return predicted_half_life

        except Exception:
            return self._fallback_half_life_prediction()

    def _extract_scalar_from_tensor(self, output):
        if isinstance(output, torch.Tensor):
            if output.numel() == 1:
                return output.item()
            elif len(output.shape) == 1:
                return output[0].item()
            elif len(output.shape) == 2:
                return output[0, 0].item()
            elif len(output.shape) == 3:
                return output[0, -1, 0].item()
            else:
                return output.flatten()[0].item()
        else:
            return float(output) if output is not None else 7.0

    def _fallback_half_life_prediction(self):
        recent_reviews = [r for r in list(self.r_queue)[-5:] if r >= 0]
        if recent_reviews:
            success_rate = sum(recent_reviews) / len(recent_reviews)
            base_half_life = 7.0
            adjustment = (success_rate - 0.5) * 5.0
            return np.clip(base_half_life + adjustment, 3.0, 15.0)
        return 7.0

    def _calculate_reward(self, action):
        current_p = np.exp(np.log(0.5) * self.days_since_last / self.current_half_life)

        if action == 1:
            review_benefit = 2.0 * current_p
            review_cost = 1.0

            if self.days_since_last > 0:
                urgency_bonus = min(0.5, self.days_since_last / 10.0)
                review_benefit += urgency_bonus

            reward = review_benefit - review_cost
        else:
            skip_penalty = -0.1 * (1 - current_p)

            if self.days_since_last > 15:
                skip_penalty -= 0.5 * (self.days_since_last - 15) / 15.0

            reward = skip_penalty

        reward *= self.reward_scale
        reward *= self.difficulty_level

        return np.clip(reward, -5.0, 5.0)

    def get_statistics(self):
        return {
            'total_reward': self.total_reward,
            'review_count': self.review_count,
            'skip_count': self.skip_count,
            'episode_length': self.episode_length,
            'current_half_life': self.current_half_life,
            'review_ratio': self.review_count / max(1, self.review_count + self.skip_count)
        }