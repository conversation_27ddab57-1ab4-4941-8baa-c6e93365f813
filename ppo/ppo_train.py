import torch
from torch.optim import <PERSON>, <PERSON><PERSON>
from torch.optim.lr_scheduler import CosineAnnealingLR, StepLR
import time
from tqdm import tqdm
import numpy as np
from collections import deque
import logging
import warnings
warnings.filterwarnings('ignore')

from memory_env import MemoryEnv
from ppo_policy import PPOPolicy


class ExperienceBuffer:
    def __init__(self, capacity=2048):
        self.capacity = capacity
        self.observations = deque(maxlen=capacity)
        self.actions = deque(maxlen=capacity)
        self.rewards = deque(maxlen=capacity)
        self.values = deque(maxlen=capacity)
        self.log_probs = deque(maxlen=capacity)
        self.dones = deque(maxlen=capacity)
        self.advantages = deque(maxlen=capacity)
        self.returns = deque(maxlen=capacity)

    def store(self, obs, action, reward, value, log_prob, done, advantage=None, return_val=None):
        self.observations.append(obs)
        self.actions.append(action)
        self.rewards.append(reward)
        self.values.append(value)
        self.log_probs.append(log_prob)
        self.dones.append(done)
        if advantage is not None:
            self.advantages.append(advantage)
        if return_val is not None:
            self.returns.append(return_val)

    def get_batch(self):
        batch = {
            'observations': list(self.observations),
            'actions': torch.stack(list(self.actions)),
            'rewards': torch.tensor(list(self.rewards), dtype=torch.float32),
            'values': torch.stack(list(self.values)),
            'log_probs': torch.stack(list(self.log_probs)),
            'dones': torch.tensor(list(self.dones), dtype=torch.float32)
        }

        if len(self.advantages) > 0:
            batch['advantages'] = torch.tensor(list(self.advantages), dtype=torch.float32)
        if len(self.returns) > 0:
            batch['returns'] = torch.tensor(list(self.returns), dtype=torch.float32)

        return batch

    def clear(self):
        self.observations.clear()
        self.actions.clear()
        self.rewards.clear()
        self.values.clear()
        self.log_probs.clear()
        self.dones.clear()
        self.advantages.clear()
        self.returns.clear()

    def __len__(self):
        return len(self.observations)


class PPOTrainer:
    def __init__(self, env, policy, lr=1e-3, gamma=0.99, eps_clip=0.2, k_epochs=4,
                 buffer_capacity=2048, gae_lambda=0.95, value_loss_coef=0.5,
                 entropy_coef=0.01, max_grad_norm=0.5, use_lr_scheduler=True,
                 optimizer_type='adam'):
        self.env = env
        self.policy = policy

        if optimizer_type.lower() == 'adamw':
            self.optimizer = AdamW(policy.parameters(), lr=lr, weight_decay=1e-4)
        else:
            self.optimizer = Adam(policy.parameters(), lr=lr)

        self.gamma = gamma
        self.eps_clip = eps_clip
        self.k_epochs = k_epochs
        self.gae_lambda = gae_lambda
        self.value_loss_coef = value_loss_coef
        self.entropy_coef = entropy_coef
        self.max_grad_norm = max_grad_norm

        self.buffer = ExperienceBuffer(capacity=buffer_capacity)

        if use_lr_scheduler:
            self.lr_scheduler = CosineAnnealingLR(self.optimizer, T_max=1000)
        else:
            self.lr_scheduler = None

        self.training_stats = {
            'total_updates': 0,
            'policy_losses': [],
            'value_losses': [],
            'entropy_losses': [],
            'kl_divergences': [],
            'clip_fractions': []
        }


    def _compute_gae(self, rewards, values, dones, next_value):
        advantages = []
        gae = 0.0

        # Convert to numpy arrays for easier computation
        if isinstance(rewards, torch.Tensor):
            rewards = rewards.cpu().numpy()
        if isinstance(values, torch.Tensor):
            values = values.cpu().numpy().flatten()
        if isinstance(dones, torch.Tensor):
            dones = dones.cpu().numpy()
        if isinstance(next_value, torch.Tensor):
            next_value = next_value.item()

        # Ensure all are numpy arrays or scalars
        rewards = np.array(rewards)
        values = np.array(values)
        dones = np.array(dones)

        for i in reversed(range(len(rewards))):
            if i == len(rewards) - 1:
                next_non_terminal = 1.0 - float(dones[i])
                next_val = float(next_value)
            else:
                next_non_terminal = 1.0 - float(dones[i])
                next_val = float(values[i + 1])

            delta = float(rewards[i]) + self.gamma * next_val * next_non_terminal - float(values[i])
            gae = delta + self.gamma * self.gae_lambda * next_non_terminal * gae
            advantages.insert(0, gae)

        return torch.tensor(advantages, dtype=torch.float32)

    def _update_policy(self, batch):
        observations = batch['observations']
        actions = batch['actions']
        old_log_probs = batch['log_probs'].detach()
        rewards = batch['rewards']
        values = batch['values'].detach()
        dones = batch['dones']

        with torch.no_grad():
            if len(observations) > 0:
                _, last_value = self.policy(observations[-1])
                if isinstance(last_value, torch.Tensor) and last_value.numel() > 1:
                    last_value = last_value[0]
            else:
                last_value = torch.tensor(0.0)

        advantages = self._compute_gae(rewards, values, dones, last_value)
        returns = advantages + values

        advantages = (advantages - advantages.mean()) / (advantages.std() + 1e-8)

        policy_losses = []
        value_losses = []
        entropy_losses = []
        kl_divs = []
        clip_fractions = []

        for epoch in range(self.k_epochs):
            epoch_policy_loss = 0
            epoch_value_loss = 0
            epoch_entropy_loss = 0
            epoch_kl_div = 0
            epoch_clip_fraction = 0

            self.optimizer.zero_grad()
            total_loss = 0

            for i, obs in enumerate(observations):
                dist, value = self.policy(obs)

                if isinstance(value, torch.Tensor) and value.numel() > 1:
                    value = value[0]

                new_log_prob = dist.log_prob(actions[i])
                if isinstance(new_log_prob, torch.Tensor) and new_log_prob.numel() > 1:
                    new_log_prob = new_log_prob[0]

                ratio = torch.exp(new_log_prob - old_log_probs[i])

                kl_div = old_log_probs[i] - new_log_prob
                epoch_kl_div += kl_div.item()

                clipped = torch.clamp(ratio, 1 - self.eps_clip, 1 + self.eps_clip)
                clip_fraction = (ratio != clipped).float().mean()
                epoch_clip_fraction += clip_fraction.item()

                surr1 = ratio * advantages[i]
                surr2 = clipped * advantages[i]
                policy_loss = -torch.min(surr1, surr2)

                if value.dim() == 0:
                    value = value.unsqueeze(0)
                target_return = returns[i]
                if target_return.dim() == 0:
                    target_return = target_return.unsqueeze(0)
                value_loss = torch.nn.functional.mse_loss(value, target_return)

                entropy = dist.entropy()
                if isinstance(entropy, torch.Tensor) and entropy.numel() > 1:
                    entropy = entropy[0]

                loss = policy_loss + self.value_loss_coef * value_loss - self.entropy_coef * entropy
                total_loss += loss

                epoch_policy_loss += policy_loss.item()
                epoch_value_loss += value_loss.item()
                epoch_entropy_loss += entropy.item()

            # 检查是否有梯度
            if total_loss.requires_grad:
                total_loss.backward()
                torch.nn.utils.clip_grad_norm_(self.policy.parameters(), self.max_grad_norm)
                self.optimizer.step()
            else:
                print(f"Warning: total_loss does not require grad at epoch {epoch}")
                # 尝试重新计算loss以获得梯度
                continue

            policy_losses.append(epoch_policy_loss / len(observations))
            value_losses.append(epoch_value_loss / len(observations))
            entropy_losses.append(epoch_entropy_loss / len(observations))
            kl_divs.append(epoch_kl_div / len(observations))
            clip_fractions.append(epoch_clip_fraction / len(observations))

        if self.lr_scheduler:
            self.lr_scheduler.step()

        self.training_stats['total_updates'] += 1
        self.training_stats['policy_losses'].extend(policy_losses)
        self.training_stats['value_losses'].extend(value_losses)
        self.training_stats['entropy_losses'].extend(entropy_losses)
        self.training_stats['kl_divergences'].extend(kl_divs)
        self.training_stats['clip_fractions'].extend(clip_fractions)

        return {
            'policy_loss': np.mean(policy_losses),
            'value_loss': np.mean(value_losses),
            'entropy_loss': np.mean(entropy_losses),
            'kl_divergence': np.mean(kl_divs),
            'clip_fraction': np.mean(clip_fractions)
        }

    def train(self, total_steps=1e5):
        obs, info = self.env.reset()
        total_steps = int(total_steps)

        episode_rewards = 0
        episode_length = 0
        episode_count = 0
        total_loss = 0
        start_time = time.time()
        update_count = 0

        pbar = tqdm(total=total_steps, desc="Training Progress")

        for step in range(total_steps):
            dist, value = self.policy(obs)
            action = dist.sample()

            if action.numel() > 1:
                action_value = action[0].item()
            else:
                action_value = action.item()

            if isinstance(value, torch.Tensor) and value.numel() > 1:
                value = value[0]

            log_prob = dist.log_prob(action)
            if isinstance(log_prob, torch.Tensor) and log_prob.numel() > 1:
                log_prob = log_prob[0]

            next_obs, reward, done, info = self.env.step(action_value)

            self.buffer.store(obs, action, reward, value, log_prob, done)

            episode_rewards += reward
            episode_length += 1

            if len(self.buffer) >= self.buffer.capacity or done:
                if len(self.buffer) > 0:
                    batch = self.buffer.get_batch()
                    update_stats = self._update_policy(batch)
                    total_loss += update_stats['policy_loss']
                    update_count += 1
                    self.buffer.clear()

            if done:
                episode_count += 1
                avg_reward = episode_rewards / episode_count if episode_count > 0 else 0
                avg_loss = total_loss / update_count if update_count > 0 else 0
                elapsed_time = time.time() - start_time

                env_stats = self.env.get_statistics()

                pbar.set_postfix({
                    'episode': episode_count,
                    'avg_reward': f"{avg_reward:.2f}",
                    'episode_length': episode_length,
                    'avg_loss': f"{avg_loss:.4f}",
                    'updates': update_count,
                    'review_ratio': f"{env_stats.get('review_ratio', 0):.2f}",
                    'elapsed': f"{elapsed_time:.0f}s"
                })

                obs, info = self.env.reset()
                episode_rewards = 0
                episode_length = 0
            else:
                obs = next_obs

            pbar.update(1)

        pbar.close()

        final_stats = {
            'total_episodes': episode_count,
            'avg_reward': episode_rewards / episode_count if episode_count > 0 else 0,
            'total_steps': total_steps,
            'avg_loss': total_loss / update_count if update_count > 0 else 0,
            'training_time': time.time() - start_time,
            'total_updates': update_count,
            'training_stats': self.training_stats
        }

        return final_stats

    def save_model(self, path):
        torch.save({
            'policy_state_dict': self.policy.state_dict(),
            'optimizer_state_dict': self.optimizer.state_dict(),
            'training_stats': self.training_stats
        }, path)

    def load_model(self, path):
        checkpoint = torch.load(path)
        self.policy.load_state_dict(checkpoint['policy_state_dict'])
        self.optimizer.load_state_dict(checkpoint['optimizer_state_dict'])
        self.training_stats = checkpoint.get('training_stats', self.training_stats)

    def evaluate(self, num_episodes=10, deterministic=True):
        self.policy.eval()
        episode_rewards = []
        episode_lengths = []

        for episode in range(num_episodes):
            obs, info = self.env.reset()
            episode_reward = 0
            episode_length = 0
            done = False

            while not done:
                with torch.no_grad():
                    action, _, _ = self.policy.get_action(obs, deterministic=deterministic)
                    action_value = action.item() if action.numel() == 1 else action[0].item()

                obs, reward, done, info = self.env.step(action_value)
                episode_reward += reward
                episode_length += 1

            episode_rewards.append(episode_reward)
            episode_lengths.append(episode_length)

        self.policy.train()

        return {
            'mean_reward': np.mean(episode_rewards),
            'std_reward': np.std(episode_rewards),
            'mean_length': np.mean(episode_lengths),
            'std_length': np.std(episode_lengths),
            'episode_rewards': episode_rewards
        }

# PPO训练
if __name__ == "__main__":
    model_path = "../model.pth"
    total_steps = 100000
    save_path = 'rl_policy.pth'

    print("\n=== 初始化训练环境 ===")
    print(f"\n加载模型: {model_path}")

    try:
        gru = torch.load(model_path, weights_only=False)
        gru.train()
        for param in gru.parameters():
            param.requires_grad = True
    except Exception as e:
        print(f"Warning: Could not load model from {model_path}. Error: {e}")
        import torch.nn as nn
        gru = nn.GRU(3, 64, batch_first=True)
        gru.train()
        for param in gru.parameters():
            param.requires_grad = True

    print("\n创建环境和策略网络")
    env = MemoryEnv(gru_model=gru)
    policy = PPOPolicy(gru)
    trainer = PPOTrainer(env, policy)

    print(f"\n=== 开始训练 ({total_steps} 步) ===")
    stats = trainer.train(total_steps=total_steps)

    print("\n=== 训练完成 ===")
    print(f"\n总训练步数: {stats['total_steps']}")
    print(f"完成回合数: {stats['total_episodes']}")
    print(f"平均回合奖励: {stats['avg_reward']:.2f}")
    print(f"平均损失: {stats['avg_loss']:.4f}")
    print(f"训练时间: {stats['training_time']:.2f} 秒")

    print(f"\n保存策略模型到: {save_path}")
    torch.save(policy.state_dict(), save_path)
    print("\n完成!")